<template>
  <view class="image-editor-page">
    <!-- 页面头部 -->
    <view class="header" v-if="false">
      <view class="header-left">
        <button class="back-btn" @click="goBack">
          <text class="back-icon">←</text>
          <text class="back-text">返回</text>
        </button>
      </view>
      <view class="header-center">
        <text class="page-title">图片编辑器</text>
      </view>
      <view class="header-right">
        <button class="save-btn" @click="saveImage">
          <text class="save-text">保存</text>
        </button>
      </view>
    </view> 
    
    <!-- 图片编辑器组件 -->
    <view class="editor-container">
     
      <ImageEditor
        ref="imageEditorRef"
        :include-ui="true"
        :options="editorOptions"
      />
    </view>
  </view>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, nextTick } from 'vue'
import ImageEditor from '@/components/ImageEditor/Index.vue'

export default defineComponent({
  components: {
    ImageEditor
  },

  // uniapp 页面生命周期
  onLoad(options: any) {
    // 将参数存储到全局变量中
    if (options) {
      (window as any).imageEditorParams = {
        imageUrl: options.imageUrl || '',
        moderationId: options.id || ''
      }
    }
  },

  onShow() {
    // 从全局变量或页面栈中获取参数
    let params = (window as any).imageEditorParams

    if (!params) {
      try {
        const pages = getCurrentPages()
        const currentPage = pages[pages.length - 1]
        const options = (currentPage as any).options || {}
        params = {
          imageUrl: options.imageUrl || '',
          moderationId: options.id || ''
        }
      } catch (error) {
        params = { imageUrl: '', moderationId: '' }
      }
    }

    // 触发 setup 中的参数更新
    if (params.imageUrl) {
      setTimeout(() => {
        const event = new CustomEvent('updateImageUrl', { detail: params })
        window.dispatchEvent(event)
      }, 100)
    }
  },

  setup() {

    // 页面参数
    const imageUrl = ref<string>('')
    const moderationId = ref<string>('')
    const imageEditorRef = ref<any>(null)
    const imageLoaded = ref<boolean>(false) // 防止重复加载图片

    // 编辑器配置选项
    const editorOptions = ref({
      cssMaxWidth: window.innerWidth - 40,
      cssMaxHeight: window.innerHeight - 200,
      usageStatistics: false,
      imageUrl: '' // 添加 imageUrl 到选项中
    })

    // 监听自定义事件来更新参数
    const handleUpdateImageUrl = (event: any) => {
      const params = event.detail
      imageUrl.value = params.imageUrl || ''
      moderationId.value = params.moderationId || ''
      editorOptions.value.imageUrl = imageUrl.value

      // 加载图片（如果还没有加载过）
      if (imageUrl.value && !imageLoaded.value) {
        setTimeout(() => {
          loadImageToEditor()
        }, 1000)
      }
    }

    // 页面加载时获取参数
    onMounted(async () => {

      // 添加事件监听器
      window.addEventListener('updateImageUrl', handleUpdateImageUrl)

      // 尝试从全局变量获取参数
      const globalParams = (window as any).imageEditorParams
      if (globalParams) {
        imageUrl.value = globalParams.imageUrl || ''
        moderationId.value = globalParams.moderationId || ''
        editorOptions.value.imageUrl = imageUrl.value
      } else {
        // 尝试从页面栈获取参数
        try {
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          const options = (currentPage as any).options || {}

          imageUrl.value = options.imageUrl || ''
          moderationId.value = options.id || ''
          editorOptions.value.imageUrl = imageUrl.value
        } catch (error) {
          console.error('获取页面参数失败:', error)
        }
      }

      // 等待组件挂载完成后加载图片（如果还没有加载过）
      if (imageUrl.value && !imageLoaded.value) {
        await nextTick()
        setTimeout(() => {
          loadImageToEditor()
        }, 1000) // 延迟加载确保编辑器初始化完成
      }
    })

    // 加载图片到编辑器
    const loadImageToEditor = () => {

      // 防止重复加载
      if (imageLoaded.value) {
        return
      }

      if (imageEditorRef.value && imageUrl.value) {
        try {
          // 使用 tui-image-editor 的 loadImageFromURL 方法
          imageEditorRef.value.invoke('loadImageFromURL', imageUrl.value, 'EditedImage')
            .then(() => {
              imageLoaded.value = true // 标记为已加载
              // 移除了图片加载成功的提示
            })
            .catch((error: any) => {
              console.error('图片加载失败:', error)
              uni.showToast({
                title: '图片加载失败',
                icon: 'none'
              })
            })
        } catch (error) {
          console.error('加载图片到编辑器失败:', error)
          uni.showToast({
            title: '编辑器调用失败',
            icon: 'none'
          })
        }
      } else {
        console.warn('编辑器或图片URL不可用:', {
          editorRef: !!imageEditorRef.value,
          imageUrl: imageUrl.value
        })
      }
    }

    // 返回上一页
    const goBack = () => {
      uni.navigateBack({
        delta: 1
      })
    }

    // 保存图片
    const saveImage = () => {

      if (!imageEditorRef.value) {
        console.error('编辑器组件引用不存在')
        uni.showToast({
          title: '编辑器未初始化',
          icon: 'none'
        })
        return
      }

      try {

        // 获取编辑后的图片数据
        const editedImageData = imageEditorRef.value.invoke('toDataURL')

        if (!editedImageData) {
          console.error('获取的图片数据为空')
          uni.showToast({
            title: '获取图片数据失败',
            icon: 'none'
          })
          return
        }

        handleSave(editedImageData)

      } catch (error) {
        console.error('获取编辑后的图片失败:', error)
        uni.showToast({
          title: '保存失败',
          icon: 'none'
        })
      }
    }

    // 处理保存事件
    const handleSave = (editedImageData?: string) => {

      if (editedImageData) {
        // 可以在这里处理保存逻辑，比如上传到服务器
        // 暂时先显示成功提示
        uni.showToast({
          title: '图片保存成功',
          icon: 'success'
        })

        // 返回到审核结果页面
        setTimeout(() => {
          uni.navigateBack({
            delta: 1
          })
        }, 1500)
      } else {
        uni.showToast({
          title: '没有可保存的图片数据',
          icon: 'none'
        })
      }
    }

    return {
      imageUrl,
      moderationId,
      imageEditorRef,
      imageLoaded,
      editorOptions,
      loadImageToEditor,
      goBack,
      saveImage,
      handleSave
    }
  }
})


</script>

<style lang="scss" scoped>
.image-editor-page {
  width: 100%;
  height: 100vh;
  background: #000000;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left,
.header-right {
  flex: 1;
}

.header-center {
  flex: 2;
  text-align: center;
}

.back-btn {
  display: flex;
  align-items: center;
  background: transparent;
  border: none;
  color: white;
  font-size: 32rpx;
  padding: 10rpx 20rpx;
  border-radius: 10rpx;
  transition: background 0.3s;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  /* 移动端适配 */
  @media (max-width: 768px) {
    font-size: 28rpx;
    padding: 8rpx 16rpx;
  }
}

.back-icon {
  font-size: 36rpx;
  margin-right: 10rpx;
}

.back-text {
  font-size: 28rpx;
}

.page-title {
  color: white;
  font-size: 36rpx;
  font-weight: bold;
}

.save-btn {
  background: #4CAF50;
  border: none;
  color: white;
  padding: 15rpx 30rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  transition: background 0.3s;
  margin-left: auto;
  display: block;

  &:hover {
    background: #45a049;
  }

  &:active {
    transform: scale(0.98);
  }
}

.save-text {
  color: white;
  font-size: 28rpx;
}

/* 编辑器容器 */
.editor-container {
  flex: 1;
  width: 100%;
  height: calc(100vh - 120rpx);
  overflow: hidden;
  color: #fff;

  /* 移动端适配 */
  @media (max-width: 768px) {
    height: calc(100vh - 60px);
    height: calc(100vh - 60px - env(safe-area-inset-top) - env(safe-area-inset-bottom));
    height: calc(100vh - 60px - constant(safe-area-inset-top) - constant(safe-area-inset-bottom));
  }
}
</style>
