// 图片上传相关类型定义

export interface ImageFile {
  id: string
  name: string
  size: number
  type: string
  url: string
  tempFilePath?: string
  file?: File
  uploadProgress?: number
  uploadStatus?: 'pending' | 'uploading' | 'success' | 'error'
  errorMessage?: string
  uploadedData?: any // 上传后的服务器响应数据
  serverUrl?: string // 服务器返回的图片URL
  moderationId?: string // 图片审核ID
  moderationResult?: any // 审核结果
}

export interface UploadConfig {
  maxSize: number // 最大文件大小（字节）
  maxCount: number // 最大文件数量
  allowedTypes: string[] // 允许的文件类型
  quality: number // 图片质量 0-1
  compress: boolean // 是否压缩
}

export interface UploadResponse {
  success: boolean
  message: string
  data?: {
    url: string
    filename: string
    size: number
  }
}

// API 上传响应类型
export interface ApiUploadResponse {
  code: number
  success?: boolean
  message?: string
  data?: {
    url: string
    filename?: string
    size?: number
    [key: string]: any
  }
  url?: string // 有些 API 直接返回 url
}

export interface CameraOptions {
  count: number
  sizeType: ('original' | 'compressed')[]
  sourceType: ('album' | 'camera')[]
  extension?: string[]
}
