<template>
  <view v-if="visible" class="glass-overlay" @click="handleOverlayClick">
    <view class="glass-sheet" @click.stop>      
      <!-- 选项列表 -->
      <view class="glass-body">
        <view class="items-grid">
          <view
            v-for="(item, index) in items"
            :key="index"
            class="glass-item"
            :class="{ 'danger': item.danger, 'disabled': item.disabled }"
            @click="handleItemClick(index, item)"
          >
            <view class="item-container">
              <view class="item-icon-wrapper">
                <view class="item-icon">
                  <!-- SVG 图标 -->
                  <image
                    v-if="item.icon && item.icon.includes('.svg')"
                    :src="item.icon"
                    class="icon-image"
                    mode="aspectFit"
                  />
                  <!-- Emoji 图标 -->
                  <text v-else class="icon-text">{{ item.icon }}</text>
                </view>
                <view v-if="item.badge" class="item-badge" :style="{ background: getBadgeColor(index) }">
                  <text class="badge-text">{{ item.badge }}</text>
                </view>
              </view>
              <view class="item-content">
                <text class="item-text">{{ item.text }}</text>
              </view>
            </view>
            <view class="item-ripple"></view>
          </view>
        </view>
      </view>
      
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface ActionSheetItem {
  text: string
  icon?: string
  badge?: string
  danger?: boolean
  disabled?: boolean
}

interface Props {
  items: ActionSheetItem[]
  visible: boolean
  maskClosable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  maskClosable: true
})

const emit = defineEmits<{
  select: [index: number, item: ActionSheetItem]
  cancel: []
  close: []
}>()

const handleItemClick = (index: number, item: ActionSheetItem) => {
  if (item.disabled) return
  emit('select', index, item)
  emit('close')
}

const handleCancel = () => {
  emit('cancel')
  emit('close')
}

const handleOverlayClick = () => {
  if (props.maskClosable) {
    handleCancel()
  }
}

// 获取徽章颜色
const getBadgeColor = (index: number) => {
  const colors = ['#ff4757', '#5352ed', '#ff6b6b', '#26de81']
  return colors[index % colors.length]
}
</script>

<style lang="scss" scoped>
.glass-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(10rpx);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  animation: fadeIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.glass-sheet {
  width: 100%;
  max-width: 750rpx;
  min-height: 200rpx;
  background: linear-gradient(270deg, #F7F3EE -2%, #EBE3D8 47%);
  backdrop-filter: blur(20rpx);
  overflow: hidden;
  animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 -20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.glass-header {
  padding: 40rpx 40rpx 20rpx;
  text-align: center;
  
  .glass-title {
    display: block;
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 12rpx;
  }
  
  .glass-subtitle {
    display: block;
    font-size: 26rpx;
    color: #666;
    opacity: 0.8;
  }
}

.glass-body {
  padding: 40rpx 30rpx 40rpx;
}

.items-grid {
  display: flex;
  justify-content: flex-start;
  gap: 30rpx;
}

.glass-item {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
  min-height: 100rpx;

  &:active {
    transform: scale(0.95);
    background: rgba(255, 255, 255, 0.9);
  }

  &.danger {
    .item-text {
      color: #ff4757;
    }
  }

  &.disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}

.item-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  position: relative;
  z-index: 2;
  text-align: left;
  padding: 20rpx;
}

.item-icon-wrapper {
  position: relative;
  margin-bottom: 10rpx;
}

.icon-image {
  width: 90rpx;
  height: 90rpx;
}

.item-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  border-radius: 20rpx;
  padding: 4rpx 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  
  .badge-text {
    font-size: 18rpx;
    color: white;
    font-weight: bold;
  }
}

.item-content {
  width: 100%;
  text-align: center;
  .item-text {
    display: block;
    font-size: 24rpx;
    color: #000;
    font-weight: 600;
    margin-bottom: 8rpx;
  }

  .item-desc {
    display: block;
    font-size: 20rpx;
    color: #666;
    opacity: 0.8;
    line-height: 1.3;
  }
}

// 移除箭头样式，新布局不需要

.item-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  opacity: 0;
  transform: scale(0);
  transition: all 0.3s ease;
}

.glass-item:active .item-ripple {
  opacity: 1;
  transform: scale(1);
}

.glass-footer {
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.5);
}

.glass-cancel {
  padding: 30rpx;
  text-align: center;
  transition: all 0.2s ease;
  
  &:active {
    background: rgba(0, 0, 0, 0.05);
  }
  
  .cancel-text {
    font-size: 32rpx;
    color: #666;
    font-weight: 500;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>
