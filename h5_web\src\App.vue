<template>
  <view id="app">
    <router-view />
  </view>
</template>

<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'

onLaunch(() => {
})

onShow(() => {
})

onHide(() => {
})
</script>

<style lang="scss">
/*每个页面公共css */
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

page {
  background-color: #f8f8f8;
}

/* 通用样式 */
.container {
  padding: 20rpx;
}

.btn {
  border-radius: 10rpx;
  font-size: 32rpx;
  padding: 20rpx 40rpx;
  margin: 20rpx 0;
}

.btn-primary {
  background-color: #007AFF;
  color: white;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

/* 自定义 Toast 样式 */
/* #ifdef H5 */
.uni-toast {
  background: rgba(255, 255, 255, 0.95) !important;
  color: #333 !important;
  border-radius: 16rpx !important;
  backdrop-filter: blur(10rpx) !important;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15) !important;
  border: 1rpx solid rgba(0, 0, 0, 0.1) !important;
}

.uni-toast .uni-toast__content {
  background: transparent !important;
  color: #333 !important;
  font-weight: bold !important;
  text-shadow: none !important;
}

.uni-toast .uni-icon_toast {
  background: #09998B !important;
  color: white !important;
  border-radius: 50% !important;
  width: 40rpx !important;
  height: 40rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-right: 16rpx !important;
}

.uni-toast .uni-icon_toast::before {
  font-size: 24rpx !important;
}
/* #endif */
</style>
