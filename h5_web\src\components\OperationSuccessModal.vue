<template>
  <view v-if="visible" class="reminder-overlay" @click="handleOverlayClick">
    <view class="reminder-modal" @click.stop>
      
      <!-- 内容区域 -->
      <view class="modal-content">
        <view class="icon-container">
          <text class="con-01">
            √
          </text>
        </view>
        <text class="reminder-text">
          {{ props.message }}
        </text>
        
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { watch, onUnmounted } from 'vue'

interface Props {
  visible: boolean
  maskClosable?: boolean
  message?: string
  autoClose?: boolean
  autoCloseDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  maskClosable: true,
  message: 'Operation successful !',
  autoClose: true,
  autoCloseDelay: 2000
})

const emit = defineEmits<{
  ok: []
  close: []
}>()

// 自动关闭定时器
let autoCloseTimer: NodeJS.Timeout | null = null

// 监听 visible 变化，设置自动关闭
watch(() => props.visible, (newVisible) => {
  if (newVisible && props.autoClose) {
    autoCloseTimer = setTimeout(() => {
      handleClose()
    }, props.autoCloseDelay)
  } else if (autoCloseTimer) {
    clearTimeout(autoCloseTimer)
    autoCloseTimer = null
  }
})

const handleOk = () => {
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer)
    autoCloseTimer = null
  }
  emit('ok')
}

const handleClose = () => {
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer)
    autoCloseTimer = null
  }
  emit('close')
}

const handleOverlayClick = () => {
  if (props.maskClosable) {
    handleClose()
  }
}

// 组件卸载时清理定时器
onUnmounted(() => {
  if (autoCloseTimer) {
    clearTimeout(autoCloseTimer)
  }
})
</script>

<style lang="scss" scoped>
.reminder-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.reminder-modal {
  width: 600rpx;
  background: white;
  border-radius: 30rpx;
  position: relative;
  animation: slideIn 0.3s ease;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.close-btn {
  position: absolute;
  top: 10rpx;
  right: 20rpx;
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
  
  &:active {
    background: rgba(0, 0, 0, 0.2);
    transform: scale(0.95);
  }
  
  .close-icon {
    font-size: 50rpx;
    color: #333;
  }
}

.modal-content {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  .icon-container {
    display: flex;
    justify-content: center;
    margin: 20rpx 0 35rpx 0;
  }

  .con-01{
    background: #09998B;
    color: #fff;
    border-radius: 50%;
    font-size: 100rpx;
    font-weight: 600;
    width: 120rpx;
    height: 120rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.reminder-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.6;
  margin-bottom: 70rpx;
  display: block;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  cursor: pointer;
  
  &:active {
    transform: translateY(2rpx);
  }
  
  .btn-text {
    color: inherit;
  }
}

.ok-btn {
  background: #FFC107;
  color: #333;
  box-shadow: 0 8rpx 24rpx rgba(255, 193, 7, 0.3);
  
  &:active {
    background: #FFB300;
    box-shadow: 0 4rpx 12rpx rgba(255, 193, 7, 0.3);
  }
}

.close-btn-bottom {
  background: #333;
  color: white;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
  
  &:active {
    background: #222;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .reminder-modal {
    width: 90%;
    margin: 0 20rpx;
  }
  
  .modal-content {
    padding: 50rpx 30rpx 30rpx;
  }
  
  .reminder-text {
    font-size: 30rpx;
    margin-bottom: 50rpx;
  }
  
  .modal-btn {
    height: 80rpx;
    font-size: 30rpx;
  }
}
</style>
