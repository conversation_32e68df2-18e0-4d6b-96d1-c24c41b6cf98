<template>
  <view v-if="visible" class="action-sheet-overlay" @click="handleOverlayClick">
    <view class="action-sheet" @click.stop>
      <!-- 标题区域 -->
      <view v-if="title" class="action-sheet-header">
        <text class="action-sheet-title">{{ title }}</text>
      </view>
      
      <!-- 选项列表 -->
      <view class="action-sheet-body">
        <view 
          v-for="(item, index) in items" 
          :key="index"
          class="action-sheet-item"
          :class="{ 'danger': item.danger }"
          @click="handleItemClick(index, item)"
        >
          <view class="item-icon" v-if="item.icon">
            <text class="icon-text">{{ item.icon }}</text>
          </view>
          <text class="item-text">{{ item.text }}</text>
          <view v-if="item.badge" class="item-badge">
            <text class="badge-text">{{ item.badge }}</text>
          </view>
        </view>
      </view>
      
      <!-- 取消按钮 -->
      <view class="action-sheet-footer">
        <view class="action-sheet-cancel" @click="handleCancel">
          <text class="cancel-text">取消</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface ActionSheetItem {
  text: string
  icon?: string
  badge?: string
  danger?: boolean
  disabled?: boolean
}

interface Props {
  title?: string
  items: ActionSheetItem[]
  visible: boolean
  maskClosable?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  maskClosable: true
})

const emit = defineEmits<{
  select: [index: number, item: ActionSheetItem]
  cancel: []
  close: []
}>()

const handleItemClick = (index: number, item: ActionSheetItem) => {
  if (item.disabled) return
  emit('select', index, item)
  emit('close')
}

const handleCancel = () => {
  emit('cancel')
  emit('close')
}

const handleOverlayClick = () => {
  if (props.maskClosable) {
    handleCancel()
  }
}
</script>

<style lang="scss" scoped>
.action-sheet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  animation: fadeIn 0.3s ease;
}

.action-sheet {
  width: 100%;
  max-width: 750rpx;
  background: white;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
  animation: slideUp 0.3s ease;
  box-shadow: 0 -10rpx 40rpx rgba(0, 0, 0, 0.1);
}

.action-sheet-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
  
  .action-sheet-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.action-sheet-body {
  padding: 20rpx 0;
}

.action-sheet-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  transition: all 0.2s ease;
  position: relative;
  
  &:active {
    background: #f8f8f8;
  }
  
  &.danger {
    .item-text {
      color: #ff4757;
    }
    
    .item-icon .icon-text {
      color: #ff4757;
    }
  }
  
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 40rpx;
    right: 40rpx;
    height: 1rpx;
    background: #f0f0f0;
  }
}

.item-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  
  .icon-text {
    font-size: 32rpx;
    color: white;
  }
}

.item-text {
  flex: 1;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.item-badge {
  background: #ff4757;
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  
  .badge-text {
    font-size: 20rpx;
    color: white;
    font-weight: bold;
  }
}

.action-sheet-footer {
  border-top: 16rpx solid #f8f8f8;
}

.action-sheet-cancel {
  padding: 30rpx 40rpx;
  text-align: center;
  transition: all 0.2s ease;
  
  &:active {
    background: #f8f8f8;
  }
  
  .cancel-text {
    font-size: 32rpx;
    color: #666;
    font-weight: 500;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

// 深色模式支持
@media (prefers-color-scheme: dark) {
  .action-sheet {
    background: #2c2c2c;
  }
  
  .action-sheet-header {
    border-bottom-color: #404040;
    
    .action-sheet-title {
      color: #fff;
    }
  }
  
  .action-sheet-item {
    &:active {
      background: #404040;
    }
    
    &:not(:last-child)::after {
      background: #404040;
    }
  }
  
  .item-text {
    color: #fff;
  }
  
  .action-sheet-footer {
    border-top-color: #404040;
  }
  
  .action-sheet-cancel {
    &:active {
      background: #404040;
    }
    
    .cancel-text {
      color: #ccc;
    }
  }
}
</style>
