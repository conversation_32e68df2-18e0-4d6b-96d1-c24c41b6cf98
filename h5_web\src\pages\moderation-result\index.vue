<template>
  <view>
    <view class="moderation-page">
      <!-- 背景图 -->
      <image class="background-image" src="/static/image/mobile-bj.png?v=20250119" mode="aspectFit" />
      
      <image class="background-left-up" src="/static/image/mobile-left-up.png" mode="aspectFit" />

      <!-- 内容区域 -->
      <view class="content-container">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
          <view class="loading-spinner"></view>
          <text class="loading-text">Loading...</text>
        </view>
        
        <!-- 错误状态 -->
        <view v-else-if="error" class="error-container">
          <text class="error-text">{{ error }}</text>
          <button class="retry-btn" @click="fetchModerationResult">Retry</button>
        </view>
        
        <!-- 成功状态 -->
        <view v-else-if="moderationData" class="result-container">
          <!-- 图片显示 -->
          <view class="image-container">
            <image
              v-if="moderationData"
              :src="moderationData.image_url"
              class="result-image draggable"
              mode="aspectFit"
              @error="handleImageError"
              @touchstart="handleTouchStart"
              @touchmove="handleTouchMove"
              @touchend="handleTouchEnd"
              @mousedown="handleMouseDown"
              @mousemove="handleMouseMove"
              @mouseup="handleMouseUp"
              @mouseleave="handleMouseUp"
              :style="imageStyle"
            />
            <view v-else class="no-image">
              <text class="no-image-text">{{ moderationData ? 'No image URL found' : 'No data' }}</text>
              <text v-if="moderationData" class="debug-text">Debug: {{ JSON.stringify(moderationData) }}</text>
            </view>
          </view>
          
          <!-- 审核信息 -->
          <view class="info-container">
            <text :class="moderationData.status === 'rejected'?'info-value-01':'info-value'">{{ moderationData.status }}</text>
          </view>
          
        </view>
      </view>
    </view>
    <!-- 图片控制按钮 -->
    <view style="width:100%;">
        <view class="image-controls">
        <button class="control-btn" @click="zoomOut">缩小</button>
        <button class="control-btn reset" @click="resetImage">重置</button>
        <button class="control-btn" @click="zoomIn">放大</button>
        <button :class="moderationData && moderationData.status === 'rejected'?'control-btn next':'control-btn'" @click="moderationData && moderationData.status !== 'rejected' && goToImageEditor()">下一步</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { API_ENDPOINTS } from '@/config/api'
// 响应式数据
const loading = ref(true)
const error = ref('')
const moderationData = ref<any>(null)
const moderationId = ref('')
let pollingTimer: any = null

const widthaaa = ref('')
const widthaaa2 = ref('')

// 缩放和拖拽相关数据
const imageScale = ref(1) // 缩放比例
const isDragging = ref(false)
const isScaling = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const imagePosition = ref({ x: 0, y: 0 })
const lastDistance = ref(0) // 上次双指距离

// 图片样式
const imageStyle = computed(() => ({
  transform: `translate(${imagePosition.value.x}px, ${imagePosition.value.y}px) scale(${imageScale.value})`,
  transition: (isDragging.value || isScaling.value || isMouseDragging.value) ? 'none' : 'transform 0.3s ease',
  cursor: (isDragging.value || isMouseDragging.value) ? 'grabbing' : 'grab'
}))



// 监听 URL 变化，重新获取数据
let lastUrl = window.location.href
const checkUrlChange = () => {
  const currentUrl = window.location.href
  if (lastUrl !== currentUrl) {
    lastUrl = currentUrl
    // URL 变化时重新初始化页面
    initPage()
  }
}

// 定时检查 URL 变化
let urlCheckTimer: any = null
const startUrlCheck = () => {
  urlCheckTimer = setInterval(checkUrlChange, 500)
}

const stopUrlCheck = () => {
  if (urlCheckTimer) {
    clearInterval(urlCheckTimer)
    urlCheckTimer = null
  }
}

// 页面显示时重新初始化
onShow(() => {
  initPage()
})

// 页面加载时初始化
onMounted(() => {
  initPage()
  startUrlCheck()
  
uni.getSystemInfo({
  
  success(res) {
    widthaaa.value = res.screenWidth
    widthaaa2.value = res.screenHeight
  }
})
})

// 页面销毁时清理定时器
onUnmounted(() => {
  stopPolling()
  stopUrlCheck()

  // 设置标记，通知首页需要刷新
  try {
    uni.setStorageSync('needRefreshHome', 'true')
  } catch (error) {
    // 静默处理错误
  }
})

const initPage = () => {
  // 重置状态
  loading.value = true
  error.value = ''
  moderationData.value = null
  stopPolling()

  // 从 URL hash 中获取参数
  const hash = window.location.hash
  const search = window.location.search

  // 尝试从 hash 和 search 中获取 id
  let match = hash.match(/id=([^&]+)/) || search.match(/id=([^&]+)/)
  const newId = match ? match[1] : ''

  // 更新 ID 并获取数据
  if (newId) {
    moderationId.value = newId
    fetchModerationResult()
  } else {
    error.value = 'Missing moderation ID'
    loading.value = false
  }
}

// 获取审核结果
const fetchModerationResult = async () => {
  if (!moderationId.value) return

  loading.value = true
  error.value = ''

  try {
    const url = API_ENDPOINTS.MODERATION_RESULT(moderationId.value)

    const response = await uni.request({
      url: url,
      method: 'GET',
      header: {
        'Content-Type': 'application/json'
      }
    })

    if (response.statusCode === 200) {
      const result = response.data as any
      if (result.code === 2000) {
        moderationData.value = result.data

        // 检查状态，如果是最终状态则停止轮询
        const finalStatuses = ['approved', 'rejected', 'manual_review']
        if (finalStatuses.includes(moderationData.value.status)) {
          stopPolling()
        } else {
          startPolling()
        }
      } else {
        error.value = result.message || 'API Error'
      }
    } else {
      error.value = `HTTP ${response.statusCode}: Request failed`
    }
  } catch (err: any) {
    error.value = err.message || 'Network error'
  } finally {
    loading.value = false
  }
}

// 处理图片加载错误
const handleImageError = () => {
  // 静默处理图片加载错误
}

// 鼠标拖拽相关变量
const isMouseDragging = ref(false)
const mouseDragStart = ref({ x: 0, y: 0 })

// 鼠标按下
const handleMouseDown = (e: MouseEvent) => {
  isMouseDragging.value = true
  mouseDragStart.value = {
    x: e.clientX - imagePosition.value.x,
    y: e.clientY - imagePosition.value.y
  }
  e.preventDefault()
}

// 鼠标移动
const handleMouseMove = (e: MouseEvent) => {
  if (isMouseDragging.value) {
    imagePosition.value = {
      x: e.clientX - mouseDragStart.value.x,
      y: e.clientY - mouseDragStart.value.y
    }
  }
  e.preventDefault()
}

// 鼠标松开
const handleMouseUp = (e: MouseEvent) => {
  isMouseDragging.value = false
  e.preventDefault()
}

// 计算两点间距离
const getDistance = (touch1: Touch, touch2: Touch) => {
  const dx = touch1.clientX - touch2.clientX
  const dy = touch1.clientY - touch2.clientY
  return Math.sqrt(dx * dx + dy * dy)
}

// 放大图片
const zoomIn = () => {
  const newScale = imageScale.value * 1.2
  if (newScale <= 3) {
    imageScale.value = newScale
  }
}

// 缩小图片
const zoomOut = () => {
  const newScale = imageScale.value / 1.2
  if (newScale >= 0.5) {
    imageScale.value = newScale
  }
}

// 重置图片
const resetImage = () => {
  imageScale.value = 1
  imagePosition.value = { x: 0, y: 0 }
}

// 跳转到图片编辑器
const goToImageEditor = () => {
  const imageUrl = moderationData.value?.image_url || ''
  const currentId = moderationId.value

  // if (!imageUrl) {
  //   uni.showToast({
  //     title: '图片信息不完整',
  //     icon: 'none'
  //   })
  //   return
  // }

  // 跳转到图片编辑器页面，传递图片URL和审核ID
  uni.navigateTo({
    url: `/pages/image-editor/index?imageUrl=${encodeURIComponent(imageUrl)}&id=${currentId}`
  })
}

// 触摸开始
const handleTouchStart = (e: TouchEvent) => {
  if (e.touches.length === 1) {
    // 单指拖拽
    isDragging.value = true
    isScaling.value = false
    const touch = e.touches[0]
    dragStart.value = {
      x: touch.clientX - imagePosition.value.x,
      y: touch.clientY - imagePosition.value.y
    }
  } else if (e.touches.length === 2) {
    // 双指缩放
    isDragging.value = false
    isScaling.value = true
    lastDistance.value = getDistance(e.touches[0], e.touches[1])
  }
  e.preventDefault()
}

// 触摸移动
const handleTouchMove = (e: TouchEvent) => {
  if (e.touches.length === 1 && isDragging.value) {
    // 单指拖拽
    const touch = e.touches[0]
    imagePosition.value = {
      x: touch.clientX - dragStart.value.x,
      y: touch.clientY - dragStart.value.y
    }
  } else if (e.touches.length === 2 && isScaling.value) {
    // 双指缩放
    const currentDistance = getDistance(e.touches[0], e.touches[1])
    const scaleChange = currentDistance / lastDistance.value

    // 限制缩放范围 0.5x - 3x
    const newScale = imageScale.value * scaleChange
    if (newScale >= 0.5 && newScale <= 3) {
      imageScale.value = newScale
    }

    lastDistance.value = currentDistance
  }
  e.preventDefault()
}

// 触摸结束
const handleTouchEnd = (e: TouchEvent) => {
  if (e.touches.length === 0) {
    // 所有手指离开
    isDragging.value = false
    isScaling.value = false
  } else if (e.touches.length === 1) {
    // 从双指变为单指，切换到拖拽模式
    isScaling.value = false
    isDragging.value = true
    const touch = e.touches[0]
    dragStart.value = {
      x: touch.clientX - imagePosition.value.x,
      y: touch.clientY - imagePosition.value.y
    }
  }
}







// 开始轮询
const startPolling = () => {
  stopPolling() // 先清除之前的定时器
  pollingTimer = setTimeout(() => {
    fetchModerationResult()
  }, 3000) // 3秒后再次请求
}

// 停止轮询
const stopPolling = () => {
  if (pollingTimer) {
    clearTimeout(pollingTimer)
    pollingTimer = null
  }
}


</script>

<style lang="scss" scoped>

.moderation-page {
  position: relative;
  width: 360px;
  left: calc(50% - 180px);
  height: 736px;
  overflow: hidden;
  top: 20rpx;
}

.image-controls {
  margin-top:20px;
  text-align: center;
  .next{
    background: #999;
  }
}

.control-btn {
  width: 70px;
  height: 35px;
  line-height: 35px;
  margin: 0 5px;
  padding: 0;
  border-radius: 15rpx;
  border: none;
  background:#000;
  color: white;
  font-size: 15px;
  font-weight: bold;
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.9);
  }

  &.back-btn {
    background: #FF6B6B;

    &:active {
      background: #FF5252;
      transform: scale(0.9);
    }
  }
}



.background-image {
  position: absolute;
  top: 0;
  left:calc(50% - 180px);
  width: 360px;
  height: 100%;
  z-index: 1;
}

.background-left-up{
  position: absolute;
  top: 13px;
  left: 15px;
  width: 172px;
  height: 180px;
  z-index: 3;
}

.content-container {
  position: relative;
  left:calc(50% - 180px);
  z-index: 2;
  width: 324px;
  padding:15px 18px;
  height: calc(100% - 30px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 120px 104px 104px 104px;
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
  border-top: 6rpx solid #09998B;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

.loading-text {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 错误状态
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.error-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
}

.error-text {
  color: white;
  font-size: 32rpx;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background: #09998B;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:active {
    background: #087A6B;
    transform: scale(0.95);
  }
}

// 结果显示
.result-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.image-container {
  position: relative;
  width: 100%;
  max-width: 360px;
  height:100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.result-image {
  width: 100%;
  height: 100%;
  object-fit: contain;

  &.draggable {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    touch-action: none;
    cursor: grab;

    &:active {
      cursor: grabbing;
    }

    &:hover {
      cursor: grab;
    }
  }
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
}

.debug-text {
  font-size: 12px;
  color: #ccc;
  margin-top: 10px;
  word-break: break-all;
  max-width: 300px;
}

.no-image-text {
  color: #666;
  font-size: 28rpx;
}

.info-container {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 20rpx;
  padding: 30rpx;
  z-index: 100;
  text-align: center;
}

.info-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
  text-align: center;
}



.info-label {
  font-size: 28rpx;
  color: #666;
  font-weight: bold;
}

.info-value {
  background:#000;
  border-radius: 25rpx;
  padding: 15rpx 25rpx;
  font-size: 68rpx;
  font-weight: 600;
  color: #fff;
}
.info-value-01 {
  background:#ff0000;
  border-radius: 25rpx;
  padding: 15rpx 25rpx;
  font-size: 68rpx;
  font-weight: 600;
  color: #fff;
}

// 响应式设计
@media screen and (max-width: 750px) {
  .content-container {
    padding: 30rpx;
    width: 80%;
  }
  
  .image-container {
  }
  
  .info-title {
    font-size: 32rpx;
  }
  
  .info-value {
    font-size: 26rpx;
  }
  info-value-01 {
    font-size: 26rpx;
  }
}
@media screen and (max-width: 480px) {
  .background-image {
    position: absolute;
    top: 0;
    left:calc(50% - 180px);
    width: 360px;
    height: 100%;
    z-index: 1;
  }
  .background-left-up{
    position: absolute;
    top: 8px;
    left: 50px;
    width: 150px;
    height: 160px;
    z-index: 3;
  }
  .content-container {
    position: relative;
    left:calc(50% - 145px);
    width: 250px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 120px 104px 104px 104px;
  }
}

@media screen and (max-width: 375px) {
  .moderation-page {
    height: 621px;
  }
  .background-image {
    position: absolute;
    top: 0;
    left:calc(50% - 160px);
    width: 320px;
    height: 621px;
    z-index: 1;
  }
  .background-left-up{
    position: absolute;
    top: 10px;
    left: 41px;
    width: 160px;
    height: 172px;
    z-index: 3;
  }
  .content-container {
    position: relative;
    left:calc(50% - 159px);
    width: 276px;
    height: 590px;
    padding: 15px 20px;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border-radius: 120px 104px 104px 104px;
  }
}

</style>
