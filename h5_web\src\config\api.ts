// API 配置
const API_CONFIG = {
  // 基础域名
  BASE_URL: 'http://aiprinth5.baopinshidai.com',
  
  // 根据环境判断API前缀
  get API_PREFIX() {
    // 开发环境使用单个 /api，生产环境使用 /api/api
    return process.env.NODE_ENV === 'production' ? '/api/api' : '/api/api'
  },
  
  // 完整的API基础路径
  get API_BASE() {
    return `${this.BASE_URL}${this.API_PREFIX}`
  }
}

// API 端点配置
export const API_ENDPOINTS = {
  // 文件上传
  UPLOAD_IMAGE: `${API_CONFIG.API_BASE}/minio/upload/image/`,
  
  // 图片审核
  IMAGE_MODERATION: `${API_CONFIG.API_BASE}/aigreen/image-moderation/`,
  
  // 审核结果查询
  MODERATION_RESULT: (id: string) => `${API_CONFIG.API_BASE}/aigreen/image-moderation/${id}/`
}

export default API_CONFIG
