// tui-image-editor 自定义主题样式
export const customTheme = {
  // image 左上角度图片
  "common.bi.image": "", // 替换logo图片 我直接不显示
  // "common.bisize.width": "0px",
  // "common.bisize.height": "0px",
  // // "common.backgroundImage": "none",
  // "common.border": "1px solid #d5cfcf",
  // "common.backgroundColor": "#fff", // 整体背景颜色

  // // header（头部）
  // // "header.backgroundImage": "none",
  // "header.border": "0px",
  // "header.backgroundColor": "#000", // 头部的背景颜色

  // // load button（上传按钮）
  // // "loadButton.backgroundColor": "#fff",
  // // "loadButton.border": "1px solid #ddd",
  // // "loadButton.color": "#222",
  // // "loadButton.fontFamily": "NotoSans, sans-serif",
  // // "loadButton.fontSize": "12px",
  "loadButton.display": "none", // 可以直接隐藏掉“上传”按钮

  // // download button（下载按钮）
  // "downloadButton.backgroundColor": "#fdba3b", // 下载按钮背景颜色
  // "downloadButton.border": "1px solid #fdba3b", // 下载按钮边框样式
  // "downloadButton.color": "#fff", // 下载按钮文字颜色
  // "downloadButton.fontFamily": "NotoSans, sans-serif",
  // "downloadButton.fontSize": "12px",
  "downloadButton.display": "none", // 可以直接隐藏掉“下载”按钮

  // rango style
  // "range.pointer.color": "#fff",
  // "range.bar.color": "#666",
  // "range.subbar.color": "#d1d1d1",

  // "range.disabledPointer.color": "#414141",
  // "range.disabledBar.color": "#282828",
  // "range.disabledSubbar.color": "#414141",

  // "range.value.color": "#fff",
  // "range.value.fontWeight": "lighter",
  // "range.value.fontSize": "11px",
  // "range.value.border": "1px solid #353535",
  // "range.value.backgroundColor": "#151515",
  // "range.title.color": "#fff",
  // "range.title.fontWeight": "lighter",

  // colorpicker style
  // "colorpicker.button.border": "1px solid #1e1e1e",
  // "colorpicker.title.color": "#fff",

  // // 菜单-普通状态 - 绿色
  // "menu.normalIcon.color": "#2d8cf0",
  // // // 菜单-选中状态 - 蓝色
  // "menu.activeIcon.color": "blue",
  // // // 菜单-禁用状态 - 灰色
  // "menu.disabledIcon.color": "grey",
  // // // 菜单-鼠标悬浮状态 - 黄色
  // "menu.hoverIcon.color": "yellow",
};

export default customTheme