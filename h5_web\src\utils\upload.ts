import type { ImageFile, UploadConfig, UploadResponse } from '../types/upload'

// 默认上传配置
export const defaultUploadConfig: UploadConfig = {
  maxSize: 10 * 1024 * 1024, // 10MB
  maxCount: 9, // 最多9张图片
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  quality: 0.8,
  compress: true
}

// 生成唯一ID
export function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

// 格式化文件大小
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 验证文件类型
export function validateFileType(file: File, allowedTypes: string[]): boolean {
  return allowedTypes.includes(file.type)
}

// 验证文件大小
export function validateFileSize(file: File, maxSize: number): boolean {
  return file.size <= maxSize
}

// 压缩图片
export function compressImage(file: File, quality: number = 0.8): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()
    
    img.onload = () => {
      // 计算压缩后的尺寸
      let { width, height } = img
      const maxWidth = 1920
      const maxHeight = 1080
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height)
        width *= ratio
        height *= ratio
      }
      
      canvas.width = width
      canvas.height = height
      
      // 绘制压缩后的图片
      ctx?.drawImage(img, 0, 0, width, height)
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            })
            resolve(compressedFile)
          } else {
            reject(new Error('压缩失败'))
          }
        },
        file.type,
        quality
      )
    }
    
    img.onerror = () => reject(new Error('图片加载失败'))
    img.src = URL.createObjectURL(file)
  })
}

// 模拟上传文件到服务器
export async function uploadFile(file: File): Promise<UploadResponse> {
  return new Promise((resolve) => {
    // 模拟上传延迟
    setTimeout(() => {
      // 模拟上传成功
      resolve({
        success: true,
        message: '上传成功',
        data: {
          url: URL.createObjectURL(file),
          filename: file.name,
          size: file.size
        }
      })
    }, 1000 + Math.random() * 2000) // 1-3秒随机延迟
  })
}
