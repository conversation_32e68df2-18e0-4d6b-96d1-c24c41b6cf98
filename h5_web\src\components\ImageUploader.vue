<template>
  <view class="image-uploader">
    <!-- 拖拽上传区域 (H5专用) -->
    <view 
      v-if="supportDrag"
      class="drag-area"
      :class="{ 'drag-over': isDragOver }"
      @drop.prevent="handleDrop"
      @dragover.prevent="handleDragOver"
      @dragleave.prevent="handleDragLeave"
      @click="chooseImage"
    >
      <view class="drag-content">
        <text class="drag-icon">📁</text>
        <text class="drag-text">拖拽图片到此处或点击选择</text>
        <text class="drag-tip">支持 JPG、PNG、GIF 格式，单个文件不超过 {{ formatFileSize(maxSize) }}</text>
      </view>
    </view>

    <!-- 普通上传按钮 -->
    <view v-else class="upload-btn-area" @click="chooseImage">
      <button class="choose-btn">
        <text class="btn-icon">📷</text>
        <text class="btn-text">选择图片</text>
      </button>
    </view>

    <!-- 图片预览列表 -->
    <view v-if="imageList.length" class="image-list">
      <view 
        v-for="(image, index) in imageList" 
        :key="image.id"
        class="image-item"
        :class="{ 'uploading': image.uploadStatus === 'uploading' }"
      >
        <image 
          :src="image.url" 
          class="preview-image"
          mode="aspectFill"
          @click="previewImage(image.url)"
        />
        
        <!-- 上传状态覆盖层 -->
        <view v-if="image.uploadStatus !== 'success'" class="status-overlay">
          <!-- 上传中 -->
          <view v-if="image.uploadStatus === 'uploading'" class="uploading-status">
            <view class="loading-spinner"></view>
            <text class="status-text">上传中 {{ image.uploadProgress }}%</text>
            <view class="progress-bar">
              <view 
                class="progress-fill" 
                :style="{ width: (image.uploadProgress || 0) + '%' }"
              ></view>
            </view>
          </view>
          
          <!-- 上传失败 -->
          <view v-else-if="image.uploadStatus === 'error'" class="error-status">
            <text class="error-icon">❌</text>
            <text class="status-text">上传失败</text>
            <button class="retry-btn" @click.stop="retryUpload(image)">重试</button>
          </view>
          
          <!-- 等待上传 -->
          <view v-else class="pending-status">
            <text class="pending-icon">⏳</text>
            <text class="status-text">等待上传</text>
          </view>
        </view>
        
        <!-- 删除按钮 -->
        <view class="delete-btn" @click.stop="removeImage(index)">
          <text class="delete-icon">×</text>
        </view>
        
        <!-- 成功标识 -->
        <view v-if="image.uploadStatus === 'success'" class="success-badge">
          <text class="success-icon">✓</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view v-if="imageList.length" class="action-buttons">
      <button 
        class="action-btn clear-btn" 
        @click="clearAll"
      >
        清空所有
      </button>
      <button 
        class="action-btn upload-btn"
        :class="{ disabled: !canUpload }"
        :disabled="!canUpload"
        @click="startUpload"
      >
        {{ uploadButtonText }}
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { ImageFile } from '../types/upload'
import { 
  generateId, 
  formatFileSize, 
  validateFileType, 
  validateFileSize,
  defaultUploadConfig 
} from '../utils/upload'

// Props
interface Props {
  maxCount?: number
  maxSize?: number
  allowedTypes?: string[]
  autoUpload?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxCount: 9,
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: () => ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  autoUpload: false
})

// Emits
const emit = defineEmits<{
  change: [images: ImageFile[]]
  upload: [image: ImageFile]
  success: [image: ImageFile]
  error: [image: ImageFile, error: string]
}>()

// 响应式数据
const imageList = ref<ImageFile[]>([])
const isDragOver = ref(false)
const isUploading = ref(false)

// 计算属性
const supportDrag = computed(() => {
  // 检测是否为H5环境且支持拖拽
  // #ifdef H5
  return true
  // #endif
  // #ifndef H5
  return false
  // #endif
})

const canUpload = computed(() => {
  return imageList.value.some(img => img.uploadStatus === 'pending') && !isUploading.value
})

const uploadButtonText = computed(() => {
  if (isUploading.value) return '上传中...'
  const pendingCount = imageList.value.filter(img => img.uploadStatus === 'pending').length
  return pendingCount > 0 ? `上传 (${pendingCount})` : '全部已上传'
})

// 方法
const chooseImage = () => {
  const remainingCount = props.maxCount - imageList.value.length
  if (remainingCount <= 0) {
    uni.showToast({
      title: `最多只能选择${props.maxCount}张图片`,
      icon: 'none'
    })
    return
  }

  uni.chooseImage({
    count: remainingCount,
    sizeType: ['compressed', 'original'],
    sourceType: ['album', 'camera'],
    success: (res) => {
      handleSelectedFiles(res.tempFilePaths)
    },
    fail: (err) => {
      console.error('选择图片失败:', err)
      uni.showToast({
        title: '选择图片失败',
        icon: 'none'
      })
    }
  })
}

const handleSelectedFiles = (filePaths: string[]) => {
  filePaths.forEach((filePath) => {
    const imageFile: ImageFile = {
      id: generateId(),
      name: `image_${Date.now()}.jpg`,
      size: 0,
      type: 'image/jpeg',
      url: filePath,
      tempFilePath: filePath,
      uploadStatus: 'pending',
      uploadProgress: 0
    }
    imageList.value.push(imageFile)
  })
  
  emit('change', imageList.value)
  
  if (props.autoUpload) {
    startUpload()
  }
}

// H5拖拽相关方法
const handleDragOver = (e: DragEvent) => {
  isDragOver.value = true
}

const handleDragLeave = (e: DragEvent) => {
  isDragOver.value = false
}

const handleDrop = (e: DragEvent) => {
  isDragOver.value = false
  const files = Array.from(e.dataTransfer?.files || [])
  
  // 过滤图片文件
  const imageFiles = files.filter(file => file.type.startsWith('image/'))
  
  if (imageFiles.length === 0) {
    uni.showToast({
      title: '请选择图片文件',
      icon: 'none'
    })
    return
  }
  
  // 检查数量限制
  const remainingCount = props.maxCount - imageList.value.length
  const filesToAdd = imageFiles.slice(0, remainingCount)
  
  if (filesToAdd.length < imageFiles.length) {
    uni.showToast({
      title: `最多只能选择${props.maxCount}张图片`,
      icon: 'none'
    })
  }
  
  // 处理文件
  filesToAdd.forEach(file => {
    // 验证文件
    if (!validateFileType(file, props.allowedTypes)) {
      uni.showToast({
        title: `不支持的文件格式: ${file.name}`,
        icon: 'none'
      })
      return
    }
    
    if (!validateFileSize(file, props.maxSize)) {
      uni.showToast({
        title: `文件过大: ${file.name}`,
        icon: 'none'
      })
      return
    }
    
    const imageFile: ImageFile = {
      id: generateId(),
      name: file.name,
      size: file.size,
      type: file.type,
      url: URL.createObjectURL(file),
      file: file,
      uploadStatus: 'pending',
      uploadProgress: 0
    }
    imageList.value.push(imageFile)
  })
  
  emit('change', imageList.value)
  
  if (props.autoUpload) {
    startUpload()
  }
}

const removeImage = (index: number) => {
  const image = imageList.value[index]
  if (image.url.startsWith('blob:')) {
    URL.revokeObjectURL(image.url)
  }
  imageList.value.splice(index, 1)
  emit('change', imageList.value)
}

const clearAll = () => {
  imageList.value.forEach(image => {
    if (image.url.startsWith('blob:')) {
      URL.revokeObjectURL(image.url)
    }
  })
  imageList.value = []
  emit('change', imageList.value)
}

const previewImage = (url: string) => {
  const urls = imageList.value.map(img => img.url)
  const current = urls.indexOf(url)
  
  uni.previewImage({
    urls: urls,
    current: current
  })
}

const startUpload = async () => {
  if (!canUpload.value) return
  
  isUploading.value = true
  
  const pendingImages = imageList.value.filter(img => img.uploadStatus === 'pending')
  
  for (const image of pendingImages) {
    await uploadSingleImage(image)
  }
  
  isUploading.value = false
}

const uploadSingleImage = async (image: ImageFile) => {
  image.uploadStatus = 'uploading'
  image.uploadProgress = 0
  
  try {
    // 模拟上传进度
    const progressInterval = setInterval(() => {
      if (image.uploadProgress! < 90) {
        image.uploadProgress = (image.uploadProgress || 0) + Math.random() * 15
      }
    }, 300)
    
    emit('upload', image)
    
    // 模拟上传延迟
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000))
    
    clearInterval(progressInterval)
    image.uploadProgress = 100
    image.uploadStatus = 'success'
    
    emit('success', image)
    
  } catch (error) {
    image.uploadStatus = 'error'
    const errorMessage = error instanceof Error ? error.message : '上传失败'
    emit('error', image, errorMessage)
  }
}

const retryUpload = (image: ImageFile) => {
  image.uploadStatus = 'pending'
  image.uploadProgress = 0
  uploadSingleImage(image)
}

onMounted(() => {
  // 组件挂载后的初始化逻辑
})
</script>

<style lang="scss" scoped>
.image-uploader {
  width: 100%;
}

.drag-area {
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  background: #fafafa;
  transition: all 0.3s ease;
  cursor: pointer;

  &.drag-over {
    border-color: #007AFF;
    background: rgba(0, 122, 255, 0.05);
  }

  .drag-content {
    .drag-icon {
      display: block;
      font-size: 80rpx;
      margin-bottom: 20rpx;
    }

    .drag-text {
      display: block;
      font-size: 32rpx;
      color: #333;
      margin-bottom: 16rpx;
    }

    .drag-tip {
      display: block;
      font-size: 24rpx;
      color: #999;
    }
  }
}

.upload-btn-area {
  text-align: center;
  padding: 40rpx 0;

  .choose-btn {
    background: linear-gradient(45deg, #007AFF, #5856D6);
    color: white;
    border: none;
    border-radius: 50rpx;
    padding: 24rpx 60rpx;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);

    .btn-icon {
      font-size: 36rpx;
    }
  }
}

.image-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 20rpx;
  margin: 40rpx 0;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f5f5f5;

  .preview-image {
    width: 100%;
    height: 100%;
    cursor: pointer;
  }

  .status-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;

    .status-text {
      font-size: 24rpx;
      margin: 8rpx 0;
    }
  }

  .uploading-status {
    .loading-spinner {
      width: 40rpx;
      height: 40rpx;
      border: 3rpx solid rgba(255, 255, 255, 0.3);
      border-top: 3rpx solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16rpx;
    }

    .progress-bar {
      width: 80%;
      height: 6rpx;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3rpx;
      overflow: hidden;
      margin-top: 16rpx;

      .progress-fill {
        height: 100%;
        background: #4CAF50;
        transition: width 0.3s ease;
      }
    }
  }

  .error-status {
    .error-icon {
      font-size: 48rpx;
      margin-bottom: 16rpx;
    }

    .retry-btn {
      background: #FF6B6B;
      color: white;
      border: none;
      border-radius: 20rpx;
      padding: 12rpx 24rpx;
      font-size: 24rpx;
      margin-top: 16rpx;
    }
  }

  .pending-status {
    .pending-icon {
      font-size: 48rpx;
      margin-bottom: 16rpx;
    }
  }

  .delete-btn {
    position: absolute;
    top: -8rpx;
    right: -8rpx;
    width: 40rpx;
    height: 40rpx;
    background: #FF6B6B;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
    z-index: 10;

    .delete-icon {
      color: white;
      font-size: 24rpx;
      font-weight: bold;
    }
  }

  .success-badge {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    width: 32rpx;
    height: 32rpx;
    background: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .success-icon {
      color: white;
      font-size: 20rpx;
      font-weight: bold;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-top: 40rpx;

  .action-btn {
    flex: 1;
    height: 80rpx;
    border: none;
    border-radius: 40rpx;
    font-size: 32rpx;
    font-weight: bold;

    &.clear-btn {
      background: #F5F5F5;
      color: #666;
    }

    &.upload-btn {
      background: linear-gradient(45deg, #4CAF50, #45A049);
      color: white;
      box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.3);

      &.disabled {
        background: #ccc;
        box-shadow: none;
      }
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
