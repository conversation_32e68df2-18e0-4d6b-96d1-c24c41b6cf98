<template>
  <view class="upload-container">
    <!-- 标题区域 -->
    <view class="header">
      <text class="title">Upload Image</text>
    </view>

    <!-- 选择图片区域 -->
    <view class="upload-area">
      <view v-if="selectedImages.length === 0" class="upload-placeholder" @click="showImageOptions">
        <image class="upload-icon" src="/static/image/upload-icon.svg" mode="aspectFit" />
      </view>

      <!-- 多张图片预览 -->
      <view v-if="selectedImages.length > 0" class="images-preview">
        <view v-for="(image, index) in selectedImages" :key="image.id" class="image-item">
          <image
            :src="image.url"
            class="preview-image"
            mode="aspectFit"
            @click="previewImage(index)"
          />
          <view class="remove-btn" @click.stop="removeImage(index)">
            <text class="remove-icon">×</text>
          </view>
          <!-- 上传状态 -->
          <view v-if="image.uploadStatus === 'error'" class="upload-error">
            <text class="error-text">上传失败</text>
          </view>
          <!-- 上传进度 -->
          <view v-if="image.uploadStatus === 'uploading'" class="upload-progress">
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: Math.min(100, Math.round(image.uploadProgress || 0)) + '%' }"></view>
            </view>
            <text class="progress-text">{{ Math.min(100, Math.round(image.uploadProgress || 0)) }}%</text>
          </view>
        </view>

        <!-- 添加更多图片按钮 -->
        <view v-if="selectedImages.length < 9" class="add-more-btn" @click="showImageOptions">
          <text class="add-icon">+</text>
          <text class="add-text">添加图片</text>
        </view>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="tips">
      <text class="tip-text">
        Attention: Depending on the network environment, please refer to the image.
        The transmission may be delayed or fail, but the upload is successful.
        The image will be automatically deleted within 48 hours.
      </text>
    </view>

        <!-- 上传进度条 -->
    <view v-if="isUploading && selectedImages.length > 0" class="global-upload-progress">
      <view class="progress-bar">
        <view
          class="progress-fill"
          :style="{ width: Math.min(100, Math.round(selectedImage.uploadProgress || 0)) + '%' }"
        ></view>
      </view>
      <text class="progress-text">{{ Math.min(100, Math.round(selectedImage.uploadProgress || 0)) }}%</text>
    </view>

        <!-- 操作按钮区域 -->
    <view class="action-buttons">

      <button
        v-if="selectedImages.length > 0"
        class="action-btn upload-btn"
        :class="{ disabled: isUploading }"
        :disabled="isUploading"
        @click="uploadImages"
      >
        <text class="btn-text">{{ isUploading ? '上传中...' : `Upload ${selectedImages.length} Image${selectedImages.length > 1 ? 's' : ''}` }}</text>
      </button>




    </view>

    <!-- 自定义选择弹窗 -->
    <GlassActionSheet
      :visible="showActionSheetVisible"
      :title="actionSheetTitle"
      :items="actionSheetItems"
      @select="handleActionSheetSelect"
      @close="showActionSheetVisible = false"
    />

    <!-- 提醒弹窗 -->
    <ReminderModal
      :visible="showReminderModal"
      @ok="handleReminderOk"
      @close="handleReminderClose"
    />

    <!-- 移除图片确认弹窗 -->
    <ReminderModal
      :visible="showRemoveModal"
      :message="'Confirm to delete !'"
      @ok="handleRemoveConfirm"
      @close="handleRemoveCancel"
    />

    <!-- 操作成功弹窗 -->
    <OperationSuccessModal
      :visible="showSuccessModal"
      :message="successMessage"
      @ok="handleSuccessOk"
      @close="handleSuccessClose"
    />


  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { API_ENDPOINTS } from '@/config/api'
import type { ImageFile } from '../../types/upload'
import { generateId } from '../../utils/upload'
import GlassActionSheet from '../../components/GlassActionSheet.vue'
import ReminderModal from '../../components/ReminderModal.vue'
import OperationSuccessModal from '../../components/OperationSuccessModal.vue'

// 响应式数据
const selectedImages = ref<ImageFile[]>([])
const isUploading = ref(false)
const showActionSheetVisible = ref(false) // 初始为false，页面加载后再显示
const isSelectingImage = ref(false) // 标记是否正在选择图片
const showReminderModal = ref(false) // 提醒弹窗状态
const showRemoveModal = ref(false) // 移除图片确认弹窗状态
const showSuccessModal = ref(false) // 成功提示弹窗状态
const successMessage = ref('') // 成功提示消息

// 重置页面状态
const resetPageState = () => {
  selectedImages.value = []
  showActionSheetVisible.value = true
  showRemoveModal.value = false
  showSuccessModal.value = false
  isUploading.value = false
}

// 检查是否需要刷新首页
const checkRefreshFlag = () => {
  try {
    const needRefresh = uni.getStorageSync('needRefreshHome')

    if (needRefresh === 'true') {
      forceResetState()
    }
  } catch (error) {
    // 静默处理错误
  }
}



// 页面可见性变化处理
const handleVisibilityChange = () => {
  if (!document.hidden) {
    // 页面变为可见时，检查是否需要刷新
    checkRefreshFlag()

    // 检查是否从其他页面返回
    const currentHash = window.location.hash
    if (currentHash === '#/pages/index/index' || currentHash === '#/' || currentHash === '') {
      resetPageState()
    }
  }
}

// 页面显示时的处理（从其他页面返回时会触发）
onShow(() => {
  // 检查是否需要刷新
  checkRefreshFlag()

  // 如果没有选中的图片且不在选择过程中，显示选择弹窗
  if (selectedImages.value.length === 0 && !showActionSheetVisible.value && !isSelectingImage.value) {
    setTimeout(() => {
      if (!isSelectingImage.value) {
        showActionSheetVisible.value = true
      }
    }, 300)
  }
})

// 浏览器窗口获得焦点时的处理
const handleWindowFocus = () => {
  checkRefreshFlag()
}

// 页面显示事件处理（浏览器前进后退）
const handlePageShow = () => {
  checkRefreshFlag()
}

// 强制重置状态的方法
const forceResetState = () => {
  // 多种方式重置 selectedImages
  selectedImages.value = []

  // 强制触发响应式更新
  setTimeout(() => {
    selectedImages.value = []
  }, 0)

  // 重置其他状态
  showRemoveModal.value = false
  showSuccessModal.value = false
  isUploading.value = false

  // 延迟显示选择弹窗，确保状态重置完成
  setTimeout(() => {
    if (selectedImages.value.length === 0) {
      showActionSheetVisible.value = true
    }
  }, 100)

  // 延迟检查并强制重置
  setTimeout(() => {
    if (selectedImages.value.length > 0) {
      selectedImages.value = []

      // 再次延迟检查
      setTimeout(() => {
        if (selectedImages.value.length > 0) {
          // 尝试重新创建 ref
          Object.assign(selectedImages, { value: [] })
        }
      }, 100)
    }
  }, 50)

  // 清除刷新标记
  try {
    uni.removeStorageSync('needRefreshHome')
  } catch (error) {
    // 静默处理错误
  }
}

// URL 变化监听
let lastUrl = ''
const checkUrlChange = () => {
  const currentUrl = window.location.href
  if (lastUrl !== currentUrl) {
    lastUrl = currentUrl

    // 如果当前是首页，检查是否需要重置
    if (currentUrl.includes('/pages/index/index') || currentUrl.endsWith('#/') || currentUrl.endsWith('/')) {
      const needRefresh = uni.getStorageSync('needRefreshHome')

      if (needRefresh === 'true') {
        forceResetState()
      } else {
        // 如果有选中的图片但没有刷新标记，可能是标记设置失败，强制重置
        if (selectedImages.value.length > 0) {
          forceResetState()
        }
      }
    }
  }
}

// 定时检查 URL 变化和刷新标记
let refreshCheckTimer: any = null
const startRefreshCheck = () => {
  lastUrl = window.location.href
  refreshCheckTimer = setInterval(() => {
    checkUrlChange()

    // 额外检查刷新标记
    const needRefresh = uni.getStorageSync('needRefreshHome')
    if (needRefresh === 'true') {
      forceResetState()
    }
  }, 500) // 每500ms检查一次
}

const stopRefreshCheck = () => {
  if (refreshCheckTimer) {
    clearInterval(refreshCheckTimer)
    refreshCheckTimer = null
  }
}

// 页面加载完成后自动显示弹窗
onMounted(() => {
  // 检查是否需要刷新
  checkRefreshFlag()

  // 只有在没有选中图片且不在选择过程中时才显示弹窗
  if (selectedImages.value.length === 0 && !isSelectingImage.value) {
    setTimeout(() => {
      if (!isSelectingImage.value) {
        showActionSheetVisible.value = true
      }
    }, 500)
  }

  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 监听窗口焦点事件
  window.addEventListener('focus', handleWindowFocus)

  // 监听页面显示事件（浏览器前进后退）
  window.addEventListener('pageshow', handlePageShow)

  // 启动定时检查
  startRefreshCheck()
})

onUnmounted(() => {
  // 清理事件监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('focus', handleWindowFocus)
  window.removeEventListener('pageshow', handlePageShow)

  // 停止定时检查
  stopRefreshCheck()
})

// 自定义弹窗配置
const actionSheetTitle = ref('选择图片')
const actionSheetItems = ref([
  {
    text: 'Camera',
    icon: '/static/image/photos-icon.svg'
  },
  {
    text: 'File',
    icon: '/static/image/gamera-icon.svg'
  },
])


// 显示图片选择选项
const showImageOptions = () => {
  showActionSheetVisible.value = true
}

// 处理自定义弹窗选择
const handleActionSheetSelect = (index: number) => {
  // 标记正在选择图片
  isSelectingImage.value = true
  showActionSheetVisible.value = false

  if (index === 0) {
    // text: 'Camera' + photos-icon.svg - 相机拍照功能
    takePhoto()
  } else if (index === 1) {
    // text: 'File' + gamera-icon.svg - 直接打开文件选择器
    chooseFromAlbum()
  }
}

// 处理提醒弹窗
const handleReminderOk = () => {
  showReminderModal.value = false
  // 可以在这里添加确认后的逻辑
}

const handleReminderClose = () => {
  showReminderModal.value = false
}

// 处理移除图片确认弹窗（已废弃，现在直接移除）
const handleRemoveConfirm = () => {
  // 这个方法已经不再使用，因为现在直接移除图片
  showRemoveModal.value = false
}

const handleRemoveCancel = () => {
  showRemoveModal.value = false
}

// 处理成功弹窗
const handleSuccessOk = () => {
  showSuccessModal.value = false
}

const handleSuccessClose = () => {
  showSuccessModal.value = false
}

// 显示成功提示
const showSuccess = (message: string) => {
  successMessage.value = message
  showSuccessModal.value = true
}





// 从文件选择图片（直接打开文件选择器，不显示任何弹窗）
const chooseFromAlbum = () => {
  // 强制使用原生文件选择器，避免 uni.chooseImage 的弹窗
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.multiple = true // 支持多选
  input.style.display = 'none'

  input.onchange = (e) => {
    // 立即关闭弹窗
    showActionSheetVisible.value = false

    const files = (e.target as HTMLInputElement).files
    if (files && files.length > 0) {
      // 限制最多选择9张图片
      const maxCount = 9
      const fileArray = Array.from(files).slice(0, maxCount)

      if (files.length > maxCount) {
        uni.showToast({
          title: `最多只能选择${maxCount}张图片`,
          icon: 'none'
        })
      }

      // 处理所有选择的图片
      const tempFilePaths: string[] = []
      let processedCount = 0

      fileArray.forEach((file, index) => {
        const reader = new FileReader()
        reader.onload = (event) => {
          const result = event.target?.result as string
          tempFilePaths[index] = result
          processedCount++

          // 所有文件都处理完成后，添加到图片列表
          if (processedCount === fileArray.length) {
            handleSelectedImages(tempFilePaths.filter(path => path)) // 过滤掉空值
            uni.showToast({
              title: `已选择${fileArray.length}张图片`,
              icon: 'success'
            })
          }
        }
        reader.onerror = () => {
          processedCount++
          if (processedCount === fileArray.length) {
            handleSelectedImages(tempFilePaths.filter(path => path))
          }
        }
        reader.readAsDataURL(file)
      })
    }
    // 清理临时元素
    document.body.removeChild(input)
  }

  input.oncancel = () => {
    document.body.removeChild(input)
  }

  document.body.appendChild(input)
  input.click() // 直接触发文件选择对话框
}

// 拍照
const takePhoto = () => {
  uni.chooseImage({
    count: 1,
    sizeType: ['compressed', 'original'],
    sourceType: ['camera'], // 只使用相机
    success: (res) => {
      // 立即关闭弹窗
      showActionSheetVisible.value = false

      const tempFilePaths = Array.isArray(res.tempFilePaths) ? res.tempFilePaths : [res.tempFilePaths]
      handleSelectedImage(tempFilePaths[0])
    },
    fail: () => {
      uni.showToast({
        title: '拍照失败',
        icon: 'none'
      })
    }
  })
}

// 处理选中的图片（支持多张）
const handleSelectedImages = (tempFilePaths: string[]) => {
  // 立即关闭选择弹窗并重置选择状态
  showActionSheetVisible.value = false
  isSelectingImage.value = false

  tempFilePaths.forEach((tempFilePath, index) => {
    const imageFile: ImageFile = {
      id: generateId(),
      name: `image_${Date.now()}_${index}.jpg`,
      size: 0, // uniapp中无法直接获取文件大小
      type: 'image/jpeg',
      url: tempFilePath,
      tempFilePath,
      uploadStatus: 'pending',
      uploadProgress: 0
    }
    selectedImages.value.push(imageFile)
  })

  // 确保弹窗保持关闭状态
  setTimeout(() => {
    showActionSheetVisible.value = false
  }, 100)

  // 显示提醒弹窗
  showReminderModal.value = true
}

// 兼容单张图片的方法
const handleSelectedImage = (tempFilePath: string) => {
  handleSelectedImages([tempFilePath])
}

// 移除图片
const removeImage = (index: number) => {
  if (selectedImages.value[index]) {
    selectedImages.value.splice(index, 1)
    uni.showToast({
      title: '图片已移除',
      icon: 'success'
    })
  }
}

// 预览图片
const previewImage = (index: number) => {
  if (selectedImages.value[index]) {
    const urls = selectedImages.value.map(img => img.url)
    uni.previewImage({
      urls: urls,
      current: index
    })
  }
}

// 上传图片（批量）
const uploadImages = async () => {
  if (selectedImages.value.length === 0 || isUploading.value) return

  isUploading.value = true
  let successCount = 0
  let errorCount = 0

  try {
    // 批量上传所有图片
    const uploadPromises = selectedImages.value.map(async (image, index) => {
      if (image.uploadStatus === 'success') {
        successCount++
        return
      }

      try {
        image.uploadStatus = 'uploading'
        image.uploadProgress = 0

        // 上传进度模拟
        const progressInterval = setInterval(() => {
          if (image.uploadProgress! < 90) {
            const currentProgress = image.uploadProgress || 0
            const increment = Math.random() * 15
            image.uploadProgress = Math.min(90, Math.round(currentProgress + increment))
          }
        }, 300)

        // 调用真实的上传 API
        const uploadResult = await uploadToServer(image.tempFilePath || '')

        clearInterval(progressInterval)
        image.uploadProgress = 100
        image.uploadStatus = 'success'

        // 保存上传结果
        image.url = uploadResult.url || image.url
        image.uploadedData = uploadResult
        image.serverUrl = uploadResult.url
        image.moderationId = uploadResult.moderationId

        successCount++

      } catch (error) {
        image.uploadStatus = 'error'
        errorCount++
      }
    })

    // 等待所有上传完成
    await Promise.allSettled(uploadPromises)

    // 处理上传结果
    if (errorCount === 0) {
      uni.showToast({
        title: `成功上传${successCount}张图片`,
        icon: 'success'
      })

      // 如果有审核ID，跳转到第一个审核结果页面
      const firstImageWithModeration = selectedImages.value.find(img => img.moderationId)
      if (firstImageWithModeration?.moderationId) {
        setTimeout(() => {
          window.location.href = `${window.location.origin}${window.location.pathname}#/pages/moderation-result/index?id=${firstImageWithModeration.moderationId}`
        }, 1000)
      }
    } else {
      uni.showToast({
        title: `上传完成：成功${successCount}张，失败${errorCount}张`,
        icon: 'none'
      })
    }

  } catch (error) {
    uni.showToast({
      title: '批量上传失败',
      icon: 'none'
    })
  } finally {
    isUploading.value = false
  }
}

// 上传到服务器的方法
const uploadToServer = async (filePath: string): Promise<{ url: string; [key: string]: any }> => {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: API_ENDPOINTS.UPLOAD_IMAGE,
      filePath: filePath,
      name: 'file', // 服务器接收文件的字段名
      header: {},
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const result = JSON.parse(res.data)
            if (result.code === 0 || result.success) {
              // 获取 file_url
              const fileUrl = result.data?.file_url

              if (fileUrl) {
                // 调用图片审核 API
                moderateImage(fileUrl).then((moderationResult: any) => {
                  resolve({
                    url: fileUrl,
                    fileUrl: fileUrl,
                    moderationId: moderationResult.id || moderationResult.data?.id,
                    ...result.data,
                    moderationResult
                  })
                }).catch((moderationError: any) => {
                  // 即使审核失败，也返回上传结果
                  resolve({
                    url: fileUrl,
                    fileUrl: fileUrl,
                    ...result.data,
                    moderationError: moderationError.message
                  })
                })
              } else {
                reject(new Error('未获取到 file_url'))
              }
            } else {
              reject(new Error(result.message || '上传失败'))
            }
          } catch (parseError) {
            reject(new Error('响应格式错误'))
          }
        } else {
          reject(new Error(`HTTP ${res.statusCode}: 上传失败`))
        }
      },
      fail: (error) => {
        reject(new Error(error.errMsg || '网络错误'))
      }
    })
  })
}

// 图片审核方法
const moderateImage = async (fileUrl: string): Promise<any> => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: API_ENDPOINTS.IMAGE_MODERATION,
      method: 'POST',
      header: {
        'Content-Type': 'application/json'
      },
      data: {
        image_url: fileUrl
      },
      success: (res) => {
        if (res.statusCode === 200) {
          try {
            const result = res.data as any
            if (result.code === 2000 || result.success) {
              // 返回完整的审核结果，包含 id 用于页面跳转
              resolve(result.data || result)
            } else {
              reject(new Error(result.message || '图片审核失败'))
            }
          } catch (parseError) {
            reject(new Error('审核响应格式错误'))
          }
        } else {
          reject(new Error(`HTTP ${res.statusCode}: 图片审核失败`))
        }
      },
      fail: (error) => {
        reject(new Error(error.errMsg || '审核网络错误'))
      }
    })
  })
}


</script>

<style lang="scss" scoped>
.upload-container {
  min-height: 100vh;
  background: #000000;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;

  .title {
    display: block;
    font-size: 48rpx;
    font-weight: bold;
    color: white;
    margin-bottom: 20rpx;
  }

}

.upload-area {
  border-radius: 20rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10rpx);
}

.upload-placeholder {
  text-align: center;

  .upload-icon {
    width: 90%;
    margin-bottom: 30rpx;
  }


}

/* 多图片预览样式 */
.images-preview {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200rpx, 1fr));
  gap: 20rpx;
  margin-top: 20rpx;
}

.image-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f5f5f5;

  .preview-image {
    width: 100%;
    height: 100%;
  }

  .remove-btn {
    position: absolute;
    top: 0rpx;
    right: 0rpx;
    width: 40rpx;
    height: 40rpx;
    background: #000;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
    z-index: 10;

    .remove-icon {
      color: white;
      font-size: 32rpx;
      margin-top: -5rpx;
    }
  }
}

.add-more-btn {
  aspect-ratio: 1;
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;

  .add-icon {
    font-size: 60rpx;
    color: #999;
  }
}

// 全局上传进度条（在上传按钮上方）
.global-upload-progress {
  margin-bottom: 30rpx;
  padding: 20rpx 30rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  backdrop-filter: blur(10rpx);

  .progress-bar {
    height: 8rpx;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4rpx;
    overflow: hidden;
    margin-bottom: 12rpx;

    .progress-fill {
      height: 100%;
      background: #FEBF00; // 黄色进度条
      transition: width 0.3s ease;
      border-radius: 4rpx;
    }
  }

  .progress-text {
    text-align: center;
    color: white;
    font-size: 28rpx;
    font-weight: bold;
  }
}



.upload-error {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 71, 87, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;

  .error-text {
    color: white;
    font-size: 24rpx;
  }
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;

  .action-btn {
    flex: 1;
    height: 88rpx;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;

    &.choose-btn {
      background: linear-gradient(45deg, #007AFF, #5856D6);
      color: white;
      box-shadow: 0 8rpx 24rpx rgba(0, 122, 255, 0.3);

      .btn-icon {
        font-size: 36rpx;
      }
    }

    &.upload-btn {
      background: #FEBF00;
      color: #000;
      box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.3);

      &.disabled {
        background: #ccc;
        box-shadow: none;
      }
    }

    &:active {
      transform: translateY(2rpx);
    }
  }
}

.tips {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  padding: 30rpx;
  margin: 40rpx 0;
  backdrop-filter: blur(10rpx);

  .tip-text {
    font-size: 26rpx;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .upload-container {
    padding: 30rpx 20rpx;
  }

  .header .title {
    font-size: 40rpx;
  }

}
</style>
