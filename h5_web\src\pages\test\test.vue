<template>
  <view class="test-page">
    <text class="test-title">测试页面</text>
    <text class="test-id">ID: {{ testId }}</text>
    <button @click="goBack" class="back-btn">返回</button>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const testId = ref('')

onMounted(() => {
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  testId.value = options.id || '无ID'
  console.log('测试页面接收到的ID:', testId.value)
})

const goBack = () => {
  uni.navigateBack()
}
</script>

<style lang="scss" scoped>
.test-page {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f0f0f0;
}

.test-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  color: #333;
}

.test-id {
  font-size: 32rpx;
  margin-bottom: 60rpx;
  color: #666;
}

.back-btn {
  background: #09998B;
  color: white;
  border: none;
  border-radius: 40rpx;
  padding: 20rpx 60rpx;
  font-size: 32rpx;
}
</style>
