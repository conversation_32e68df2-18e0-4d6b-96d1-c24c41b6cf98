<template>
  <div class="image-editor-wrap">
    <div ref="tuiImageEditor" style="height: 100%; width: 100%"></div>
    <button class="download-btn" @click="handleDownload">保存</button>
  </div>
</template>

<script lang="ts">
import 'tui-color-picker/dist/tui-color-picker.css'
import 'tui-image-editor/dist/tui-image-editor.css'
import ImageEditor from 'tui-image-editor'
import { defineComponent, onBeforeUnmount, onMounted, ref } from 'vue'
import localeCN from '@/components/ImageEditor/i18n/localeCN.js'
import customTheme from '@/components/ImageEditor/utils/theme.js'

const includeUIOptions = {
  includeUI: {
    initMenu: 'filter',
    locale: localeCN,
    theme: customTheme, // 样式修改
  },
}
const editorDefaultOptions = {
  cssMaxWidth: 700,
  cssMaxHeight: 500,
  usageStatistics: false,
}

export default defineComponent({
  props: {
    includeUi: {
      type: Boolean,
      default: true,
    },
    options: {
      type: Object,
      default() {
        return editorDefaultOptions
      },
    },
  },
  setup(props, { emit }) {
    const tuiImageEditor = ref(null)
    let editorInstance = null
    let handleResize: (() => void) | null = null

    const addEventListener = () => {
      Object.keys(emit).forEach((eventName) => {
        editorInstance.on(eventName, (...args) => emit(eventName, ...args))
      })
    }

    const getRootElement = () => {
      return tuiImageEditor.value
    }

    const invoke = (methodName, ...args) => {
      let result = null
      if (editorInstance[methodName]) {
        result = editorInstance[methodName](...args)
      } else if (methodName.indexOf('.') > -1) {
        const func = getMethod(editorInstance, methodName)
        if (typeof func === 'function') {
          result = func(...args)
        }
      }
      return result
    }

    const getMethod = (instance, methodName) => {
      const { first, rest } = parseDotMethodName(methodName)
      const isInstance = instance.constructor.name !== 'Object'
      const type = typeof instance[first]
      let obj

      if (isInstance && type === 'function') {
        obj = instance[first].bind(instance)
      } else {
        obj = instance[first]
      }

      if (rest.length > 0) {
        return getMethod(obj, rest)
      }

      return obj
    }

    const parseDotMethodName = (methodName) => {
      const firstDotIdx = methodName.indexOf('.')
      let firstMethodName = methodName
      let restMethodName = ''

      if (firstDotIdx > -1) {
        firstMethodName = methodName.substring(0, firstDotIdx)
        restMethodName = methodName.substring(firstDotIdx + 1, methodName.length)
      }

      return {
        first: firstMethodName,
        rest: restMethodName,
      }
    }

    onMounted(() => {
      try {

        let options = props.options
        if (props.includeUi) {
          options = Object.assign({}, includeUIOptions, props.options)
        }

        // 确保容器存在
        if (!tuiImageEditor.value) {
          throw new Error('ImageEditor 容器元素不存在')
        }

        // 检查 ImageEditor 构造函数
        if (typeof ImageEditor !== 'function') {
          throw new Error('ImageEditor 构造函数不可用')
        }

        editorInstance = new ImageEditor(tuiImageEditor.value, options)

        addEventListener()

        // 添加窗口大小变化监听器（移动端设备旋转等）
        handleResize = () => {
          if (editorInstance && editorInstance.ui) {
            try {
              editorInstance.ui.resizeEditor()
              console.log('编辑器尺寸已调整')
            } catch (error) {
              console.warn('编辑器尺寸调整失败:', error)
            }
          }
        }

        window.addEventListener('resize', handleResize)
        window.addEventListener('orientationchange', handleResize)

        // 延迟一点时间确保编辑器完全初始化
        setTimeout(() => {
          console.log('ImageEditor 初始化完成')

          // 移动端特殊处理
          if (window.innerWidth <= 768) {
            console.log('检测到移动设备，应用移动端优化')
            // 调整编辑器尺寸以适应移动端
            if (editorInstance && editorInstance.ui) {
              try {
                editorInstance.ui.resizeEditor()
              } catch (error) {
                console.warn('移动端尺寸调整失败:', error)
              }
            }
          }

          if (props.options?.imageUrl) {
            console.log('自动加载图片:', props.options.imageUrl)
            loadImage(props.options.imageUrl)
          }
        }, 500)

      } catch (error) {
        
      }
    })

    onBeforeUnmount(() => {
      // 移除窗口事件监听器
      if (handleResize) {
        window.removeEventListener('resize', handleResize)
        window.removeEventListener('orientationchange', handleResize)
      }

      // 清理编辑器
      if (editorInstance) {
        Object.keys(emit).forEach((eventName) => {
          try {
            editorInstance.off(eventName)
          } catch (error) {
            console.warn('移除事件监听器失败:', eventName, error)
          }
        })

        try {
          editorInstance.destroy()
        } catch (error) {
          console.warn('销毁编辑器实例失败:', error)
        }

        editorInstance = null
      }
    })

    // 加载图像
    const loadImage = (imageUrl?: string) => {

      // 如果没有传入 imageUrl，尝试从 props.options 中获取
      const finalImageUrl = imageUrl || props.options?.imageUrl

      if (!finalImageUrl) {
        return Promise.reject('没有提供图片URL')
      }

      if (!editorInstance) {
        return Promise.reject('编辑器实例不存在')
      }


      // 尝试不同的加载方式
      try {
        // 方法1: 直接使用 loadImageFromURL
        if (typeof editorInstance.loadImageFromURL === 'function') {
          return editorInstance
            .loadImageFromURL(finalImageUrl, 'User Image')
            .then((sizeValue: any) => {
              return handleImageLoadSuccess(sizeValue)
            })
            .catch((e: any) => {
              return tryAlternativeLoad(finalImageUrl)
            })
        } else {
          console.warn('loadImageFromURL 方法不存在，尝试替代方法')
          return tryAlternativeLoad(finalImageUrl)
        }
      } catch (error) {
        return tryAlternativeLoad(finalImageUrl)
      }
    }

    // 处理图片加载成功
    const handleImageLoadSuccess = (sizeValue: any) => {
      try {
        if (editorInstance && editorInstance.ui) {
          editorInstance.ui.activeMenuEvent()
          editorInstance.ui.resizeEditor({ imageSize: sizeValue })
        }
        console.log('图片编辑器UI更新完成')
        return sizeValue
      } catch (error) {
        return sizeValue
      }
    }

    const handleDownload = () => {
      if (!editorInstance) {
        console.error('编辑器未初始化');
        uni.showToast({ title: '编辑器未准备好', icon: 'none' });
        return;
      }

      uni.showLoading({ title: '正在生成图片...', mask: true });

      try {
        // 获取Base64数据
        const base64Data = editorInstance.toDataURL({
          format: 'png',
          quality: 0.95,
          multiplier: 2
        });

        // 转换Base64为Blob对象
        const byteString = atob(base64Data.split(',')[1]);
        const mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0];
        const ab = new ArrayBuffer(byteString.length);
        const ia = new Uint8Array(ab);
        
        for (let i = 0; i < byteString.length; i++) {
          ia[i] = byteString.charCodeAt(i);
        }
        
        const blob = new Blob([ab], { type: mimeString });

        // 创建对象URL
        const blobUrl = URL.createObjectURL(blob);
        
        // 创建下载链接
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = `image_${new Date().getTime()}.png`;
        
        // 触发下载
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        // 释放内存
        setTimeout(() => {
          URL.revokeObjectURL(blobUrl);
        }, 100);
        
      } catch (error) {
        uni.showToast({ title: '下载失败: ' + error.message, icon: 'none' });
      } finally {
        uni.hideLoading();
      }
    }

    // 尝试替代的加载方法
    const tryAlternativeLoad = (imageUrl: string) => {

      return new Promise((resolve, reject) => {
        // 创建一个图片元素来预加载图片
        const img = new Image()

        // 设置超时
        const timeout = setTimeout(() => {
          reject(new Error('图片加载超时'))
        }, 10000) // 10秒超时

        img.onload = () => {
          clearTimeout(timeout)

          try {
            // 尝试使用其他可能的方法
            if (editorInstance && typeof editorInstance.addImageObject === 'function') {
              editorInstance.addImageObject(imageUrl)
              resolve({ width: img.width, height: img.height })
            } else if (editorInstance && typeof editorInstance.loadImage === 'function') {
              editorInstance.loadImage(imageUrl, 'User Image')
              resolve({ width: img.width, height: img.height })
            } else {
              console.warn('没有找到可用的图片加载方法，可用方法:', Object.getOwnPropertyNames(editorInstance || {}))
              // 即使没有找到方法，也认为预加载成功
              resolve({ width: img.width, height: img.height })
            }
          } catch (error) {
            // 即使方法调用失败，图片预加载成功也算成功
            resolve({ width: img.width, height: img.height })
          }
        }

        img.onerror = (error) => {
          clearTimeout(timeout)
          console.error('图片预加载失败，错误:', error)
          console.error('图片URL:', imageUrl)

          // 检查是否是跨域问题
          if (imageUrl.startsWith('http') && !imageUrl.startsWith(window.location.origin)) {
            console.log('可能是跨域问题，尝试不设置 crossOrigin')
            const img2 = new Image()
            img2.onload = () => {
              console.log('不设置 crossOrigin 的图片加载成功')
              resolve({ width: img2.width, height: img2.height })
            }
            img2.onerror = () => {
              reject(new Error('图片加载失败，可能是网络问题或图片不存在'))
            }
            img2.src = imageUrl
          } else {
            reject(new Error('图片加载失败'))
          }
        }

        // 尝试设置 crossOrigin，如果失败则不设置
        try {
          img.crossOrigin = 'anonymous'
        } catch (e) {
          console.warn('无法设置 crossOrigin:', e)
        }

        console.log('开始加载图片:', imageUrl)
        img.src = imageUrl
      })
    }

    // 测试编辑器功能
    const testEditor = () => {
      console.log('=== 测试编辑器功能 ===')
      console.log('编辑器实例:', editorInstance)

      if (!editorInstance) {
        console.error('编辑器实例不存在')

        return
      }

      console.log('编辑器实例类型:', typeof editorInstance)
      console.log('编辑器实例构造函数:', editorInstance.constructor.name)
      console.log('可用方法:', Object.getOwnPropertyNames(editorInstance))

      // 测试获取图片数据
      try {
        if (typeof editorInstance.toDataURL === 'function') {
          const imageData = editorInstance.toDataURL()
          console.log('获取图片数据成功:', imageData ? '有数据' : '无数据')

        } else {
          console.warn('toDataURL 方法不存在')
        }
      } catch (error) {
        console.error('获取图片数据失败:', error)
      }

      // 测试加载图片 - 使用用户当前的图片URL
      const currentImageUrl = props.options?.imageUrl
      if (currentImageUrl) {
        console.log('测试加载当前图片:', currentImageUrl)

        try {
          loadImage(currentImageUrl)
            .then(() => {
              console.log('当前图片加载成功')

            })
            .catch((error) => {
              console.error('当前图片加载失败:', error)

            })
        } catch (error) {
          console.error('图片加载异常:', error)

        }
      } else {
        console.log('没有可用的图片URL进行测试')

      }
    }

    // 简单的图片测试（不依赖编辑器）
    const simpleImageTest = () => {
      console.log('=== 简单图片测试 ===')
      const imageUrl = props.options?.imageUrl

      if (!imageUrl) {
        console.error('没有图片URL')

        return
      }

      console.log('测试图片URL:', imageUrl)

      const img = new Image()
      const startTime = Date.now()

      img.onload = () => {
        const loadTime = Date.now() - startTime
      }

      img.onerror = (error) => {
        
      }

      img.src = imageUrl
    }

    return {
      tuiImageEditor,
      invoke,
      getRootElement,
      loadImage,
      handleDownload
    }
  },
})
</script>

<style lang="scss" scoped>
.image-editor-wrap {
  height: 100vh;
  width: 100%;
  overflow: hidden;
  position: relative;

  /* 移动端适配 */
  @media (max-width: 768px) {
    height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
    height: calc(100vh - constant(safe-area-inset-top) - constant(safe-area-inset-bottom));
  }
}
.download-btn {
  position: fixed;
  top: 8px;
  right: 20px;
  background: #4CAF50;
  color: white;
  border: none;
  padding:0px 24px;
  border-radius: 8px;
  cursor: pointer;
  z-index: 10;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  transition: all 0.3s ease;

  &:hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  }
}
/* 修改 tui-image-editor 样式 */
::v-deep .tui-image-editor-header-logo > img {
  display: none;
}

/* 隐藏滚动条 */
::v-deep .tui-image-editor {
  overflow: hidden !important;
}

::v-deep .tui-image-editor-canvas-container {
  overflow: hidden !important;
}

::v-deep .tui-image-editor-wrap {
  overflow: hidden !important;
}

/* 隐藏右侧滚动条 */
::v-deep .tui-image-editor-main {
  overflow: hidden !important;
}

::v-deep .tui-image-editor-main-container {
  overflow: hidden !important;
}

/* 增强移动端适配 */
@media (max-width: 400px) {
  .download-btn{
    display: none;
  }
  /* 调整主容器 */
  ::v-deep .tui-image-editor-container {
    flex-direction: column !important;
    .tui-image-editor-help-menu{
      width: 100%;
      .tui-image-editor-item{
        padding: 8px 5px 8px 5px;
        margin: 0 2px;
      }
    }
  }
  
  ::v-deep .tui-image-editor-controls{
    height: 68px;
  }
  /* 调整菜单栏布局 */
  ::v-deep .tui-image-editor-menu {
    flex-wrap: wrap !important;
    max-height: 68px !important;
    overflow-x: auto !important;
    overflow-y: hidden !important;
    padding: 5px 0 !important;
    .tui-image-editor-item{
      padding: 3px 4px 0px 4px;
    }
  }
  
  /* 调整菜单项 */
  ::v-deep .tui-image-editor-menu-item {
    min-width: 40px !important;
    height: 40px !important;
    margin: 2px !important;
    padding: 5px !important;
  }
  
  /* 调整子菜单 */
  ::v-deep .tui-image-editor-submenu {
    height: 150px;
    padding: 10rpx;
    overflow-x: auto;
    font-size: 28rpx;
    display: flex!important;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.1) !important;

  }
  
  /* 调整画布区域 */
  ::v-deep .tui-image-editor-canvas-container {
    height: calc(100vh - 150px) !important;
  }
  
  /* 调整按钮 */
  ::v-deep .tui-image-editor-button {
    padding: 6px 10px !important;
    margin: 3px !important;
    font-size: 12px !important;
  }
    
  /* 调整颜色选择器 */
  ::v-deep .tui-colorpicker-container {
    width: 100% !important;
  }
}
.get-image-data-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: #4CAF50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  z-index: 1000;
  font-size: 14px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.get-image-data-btn:hover {
  background: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}
</style>